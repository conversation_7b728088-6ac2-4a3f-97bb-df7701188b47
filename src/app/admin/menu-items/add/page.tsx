"use client";

import { useLocale } from "@/contexts/LocaleContext";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { createMenuItem, getUserCategories } from "@/lib/firebase/firestore";
import { uploadFile, generateUniqueFileName } from "@/lib/firebase/storage";
import { useRouter } from "next/navigation";
import { StockStatus, Category } from "@/types/models";
import { useAuth } from "@/contexts/AuthContext";

export default function AddMenuItemPage() {
  const { t, isClient, locale } = useLocale();
  const isRTL = locale === 'ar';
  const router = useRouter();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    categoryId: '',
    price: '',
    prepTime: '',
    initialStock: '',
    description: '',
    image: '',
    caffeine: '',
    ingredients: '',
    allergens: '',
    isFeatured: false,
    availableForDelivery: false,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  
  useEffect(() => {
    if (user?.uid) {
      loadCategories();
    }
  }, [user?.uid]);
  
  // Clean up preview URL when component unmounts
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);
  
  const loadCategories = async () => {
    try {
      if (!user?.uid) return;
      
      const userCategories = await getUserCategories(user.uid);
      setCategories(userCategories);
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Failed to load categories");
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };
  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size exceeds 5MB limit');
        return;
      }
      
      // Check file type
      if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
        setError('Only JPG and PNG files are allowed');
        return;
      }
      
      setSelectedFile(file);
      
      // Create and set preview URL
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);
      
      // Clear any previous errors
      setError('');
    }
  };
  
  const handleBrowseClick = () => {
    // Trigger the hidden file input click
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  const uploadImage = async (): Promise<string> => {
    if (!selectedFile || !user?.uid) {
      return formData.image; // Return existing image URL if no new file
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      const uniqueFileName = generateUniqueFileName(user.uid, selectedFile.name);
      const path = `menu-items/${user.uid}/${uniqueFileName}`;
      
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress < 90 ? newProgress : prev;
        });
      }, 300);
      
      // Upload the file
      const downloadURL = await uploadFile(selectedFile, path);
      
      // Clear the interval and set progress to 100%
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      return downloadURL;
    } catch (err) {
      console.error('Error uploading image:', err);
      throw new Error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    
    try {
      if (!user?.uid) {
        throw new Error('User not authenticated');
      }
      
      if (!formData.title || !formData.categoryId || !formData.price) {
        throw new Error('Please fill in all required fields');
      }
      
      // Upload image if selected
      let imageUrl = formData.image;
      if (selectedFile) {
        imageUrl = await uploadImage();
      }
      
      // Prepare menu item data
      const menuItemData = {
        userId: user.uid,
        title: formData.title,
        categoryId: formData.categoryId,
        price: parseFloat(formData.price),
        prepTime: formData.prepTime ? parseInt(formData.prepTime) : 0,
        description: formData.description,
        image: imageUrl,
        caffeine: formData.caffeine || undefined,
        ingredients: formData.ingredients || undefined,
        allergens: formData.allergens || undefined,
        stockStatus: parseInt(formData.initialStock) > 0 ? StockStatus.IN_STOCK : StockStatus.OUT_OF_STOCK,
        stockQuantity: formData.initialStock ? parseInt(formData.initialStock) : 0,
        isFeatured: formData.isFeatured,
        isAvailableForDelivery: formData.availableForDelivery,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Create the menu item
      await createMenuItem(menuItemData);
      
      // Redirect to menu items page
      router.push('/admin/menu-items');
    } catch (err) {
      console.error('Error adding menu item:', err);
      setError(err instanceof Error ? err.message : 'Failed to add menu item');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="space-y-6" dir={isRTL ? "rtl" : "ltr"}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            {isClient ? t('admin.addMenuItem.title') : 'Add New Menu Item'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isClient ? t('admin.addMenuItem.description') : 'Create a new item for your menu'}
          </p>
        </div>
        <Link
          href="/admin/menu-items"
          className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"
        >
          <i className={`fa-solid fa-times ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
          {isClient ? t('admin.addMenuItem.cancel') : 'Cancel'}
        </Link>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-lg">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.addMenuItem.itemName') : 'Item Name'} *
              </label>
              <input 
                type="text" 
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                placeholder={isClient ? t('admin.addMenuItem.itemNamePlaceholder') : 'e.g., Cappuccino'}
                required
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.addMenuItem.category') : 'Category'} *
              </label>
              <select 
                name="categoryId"
                value={formData.categoryId}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                required
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.addMenuItem.price') : 'Price ($)'} *
              </label>
              <input 
                type="number" 
                name="price"
                value={formData.price}
                onChange={handleChange}
                step="0.01" 
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                placeholder="0.00"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.addMenuItem.prepTime') : 'Preparation Time (mins)'}
              </label>
              <input 
                type="number" 
                name="prepTime"
                value={formData.prepTime}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                placeholder="15"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.addMenuItem.initialStock') : 'Initial Stock'}
              </label>
              <input 
                type="number" 
                name="initialStock"
                value={formData.initialStock}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                placeholder="100"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {isClient ? t('admin.addMenuItem.description') : 'Description'}
            </label>
            <textarea
              rows={4}
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
              placeholder={isClient ? t('admin.addMenuItem.descriptionPlaceholder') : 'Describe your menu item...'}
            ></textarea>
          </div>

          {/* Nutritional Information Section */}
          <div className="space-y-4">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
                <i className="fa-solid fa-leaf text-green-500 mr-2"></i>
                {isClient ? t('admin.addMenuItem.nutritionalInfo') : 'Nutritional Information'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {isClient ? t('admin.addMenuItem.nutritionalInfoDescription') : 'Provide nutritional details to help customers make informed choices'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <i className="fa-solid fa-bolt text-yellow-500 mr-1"></i>
                  {isClient ? t('admin.addMenuItem.caffeine') : 'Caffeine Content'}
                </label>
                <input
                  type="text"
                  name="caffeine"
                  value={formData.caffeine}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  placeholder={isClient ? t('admin.addMenuItem.caffeinePlaceholder') : '95mg, Low caffeine, Caffeine-free'}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('admin.addMenuItem.caffeineHint') : 'Enter caffeine amount (e.g., "95mg", "Low caffeine", "Caffeine-free") or leave empty'}
                </p>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <i className="fa-solid fa-list text-blue-500 mr-1"></i>
                  {isClient ? t('admin.addMenuItem.ingredients') : 'Ingredients'}
                </label>
                <input
                  type="text"
                  name="ingredients"
                  value={formData.ingredients}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  placeholder={isClient ? t('admin.addMenuItem.ingredientsPlaceholder') : 'Espresso, steamed milk, milk foam'}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('admin.addMenuItem.ingredientsHint') : 'List main ingredients separated by commas'}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <i className="fa-solid fa-triangle-exclamation text-red-500 mr-1"></i>
                {isClient ? t('admin.addMenuItem.allergens') : 'Allergens'}
              </label>
              <input
                type="text"
                name="allergens"
                value={formData.allergens}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                placeholder={isClient ? t('admin.addMenuItem.allergensPlaceholder') : 'Milk, nuts, gluten'}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {isClient ? t('admin.addMenuItem.allergensHint') : 'List potential allergens separated by commas'}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {isClient ? t('admin.addMenuItem.itemImage') : 'Item Image'}
            </label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              {/* Hidden file input */}
              <input 
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept="image/jpeg, image/png"
                className="hidden"
              />
              
              {/* Preview area */}
              {previewUrl && (
                <div className="mb-4">
                  <img 
                    src={previewUrl} 
                    alt="Preview" 
                    className="max-h-40 mx-auto object-contain rounded-lg"
                  />
                </div>
              )}
              
              {/* Upload progress */}
              {isUploading && (
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-4">
                  <div 
                    className="bg-[#56999B] dark:bg-[#5DBDC0] h-2.5 rounded-full" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
              
              <div className="space-y-4">
                <div className="flex flex-col items-center space-y-2">
                  <i className="fa-solid fa-cloud-upload text-4xl text-gray-400 dark:text-gray-500"></i>
                  <p className="text-gray-600 dark:text-gray-400">
                    {isClient ? t('admin.addMenuItem.dragAndDrop') : 'Drag and drop your image here, or'}
                  </p>
                  <button 
                    type="button" 
                    onClick={handleBrowseClick}
                    className="px-4 py-2 bg-[#83EAED] dark:bg-[#5DBDC0] text-[#56999B] dark:text-white rounded-lg hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D]"
                    disabled={isUploading}
                  >
                    {isClient ? t('admin.addMenuItem.browseFiles') : 'Browse Files'}
                  </button>
                </div>
                
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center">
                    <span className="bg-white dark:bg-[#1d2127] px-2 text-sm text-gray-500 dark:text-gray-400">
                      or
                    </span>
                  </div>
                </div>
                
                <input
                  type="text"
                  name="image"
                  value={formData.image}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg"
                  placeholder="Paste image URL here"
                  disabled={isUploading || !!selectedFile}
                />
                
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('admin.addMenuItem.maxFileSize') : 'Maximum file size: 5MB (JPG, PNG)'}
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Options
            </label>
            <div className="flex flex-wrap gap-4">
              <label className="flex items-center">
                <input 
                  type="checkbox"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={handleChange}
                  className="form-checkbox text-[#56999B] dark:text-[#5DBDC0] rounded"
                />
                <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                  {isClient ? t('admin.addMenuItem.featuredItem') : 'Featured Item'}
                </span>
              </label>
              <label className="flex items-center">
                <input 
                  type="checkbox"
                  name="availableForDelivery"
                  checked={formData.availableForDelivery}
                  onChange={handleChange}
                  className="form-checkbox text-[#56999B] dark:text-[#5DBDC0] rounded"
                />
                <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                  {isClient ? t('admin.addMenuItem.availableForDelivery') : 'Available for Delivery'}
                </span>
              </label>
            </div>
          </div>

          <div className={`flex ${isRTL ? 'justify-start' : 'justify-end'} ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'} pt-6 border-t border-gray-200 dark:border-gray-700`}>
            <Link
              href="/admin/menu-items"
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832]"
            >
              {isClient ? t('admin.addMenuItem.cancel') : 'Cancel'}
            </Link>
            <button 
              type="submit" 
              className="px-6 py-2 bg-[#56999B] dark:bg-[#5DBDC0] text-white rounded-lg hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D] disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || isUploading}
            >
              {isSubmitting 
                ? 'Saving...' 
                : (isClient ? t('admin.addMenuItem.publishItem') : 'Publish Item')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 