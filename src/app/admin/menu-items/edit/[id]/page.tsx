"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { getMenuItem, updateMenuItem, getUserCategories } from "@/lib/firebase/firestore";
import { Category, MenuItem, StockStatus } from "@/types/models";
import { useAuth } from "@/contexts/AuthContext";

export default function EditMenuItemPage() {
  const { t, isClient, locale } = useLocale();
  const isRTL = locale === 'ar';
  const router = useRouter();
  const { user } = useAuth();
  const params = useParams();
  const id = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    categoryId: '',
    price: '',
    prepTime: '',
    stockQuantity: '',
    description: '',
    image: '',
    caffeine: '',
    ingredients: '',
    allergens: '',
    stockStatus: '',
    isFeatured: false,
    isAvailableForDelivery: false,
    isActive: true,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  
  useEffect(() => {
    if (user?.uid && id) {
      loadMenuItem();
      loadCategories();
    }
  }, [user?.uid, id]);
  
  const loadMenuItem = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const menuItem = await getMenuItem(id);
      
      if (!menuItem) {
        setError('Menu item not found');
        return;
      }
      
      // Check if this menu item belongs to the current user
      if (menuItem.userId !== user?.uid) {
        setError('You do not have permission to edit this item');
        return;
      }
      
      // Populate form data
      setFormData({
        title: menuItem.title,
        categoryId: menuItem.categoryId,
        price: menuItem.price.toString(),
        prepTime: menuItem.prepTime?.toString() || '',
        stockQuantity: menuItem.stockQuantity.toString(),
        description: menuItem.description,
        image: menuItem.image,
        caffeine: menuItem.caffeine || '',
        ingredients: menuItem.ingredients || '',
        allergens: menuItem.allergens || '',
        stockStatus: menuItem.stockStatus,
        isFeatured: menuItem.isFeatured,
        isAvailableForDelivery: menuItem.isAvailableForDelivery,
        isActive: menuItem.isActive,
      });
    } catch (err) {
      console.error('Error loading menu item:', err);
      setError('Failed to load menu item');
    } finally {
      setIsLoading(false);
    }
  };
  
  const loadCategories = async () => {
    try {
      if (!user?.uid) return;
      
      const userCategories = await getUserCategories(user.uid);
      setCategories(userCategories);
    } catch (err) {
      console.error('Error loading categories:', err);
      setError('Failed to load categories');
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    
    try {
      if (!user?.uid) {
        throw new Error('User not authenticated');
      }
      
      if (!formData.title || !formData.categoryId || !formData.price) {
        throw new Error('Please fill in all required fields');
      }
      
      // Calculate stock status if it's being set manually
      let stockStatus = formData.stockStatus as StockStatus;
      if (formData.stockQuantity) {
        const quantity = parseInt(formData.stockQuantity);
        if (quantity <= 0) {
          stockStatus = StockStatus.OUT_OF_STOCK;
        } else if (quantity < 10) {
          stockStatus = StockStatus.LOW_STOCK;
        } else {
          stockStatus = StockStatus.IN_STOCK;
        }
      }
      
      // Prepare menu item data for update
      const menuItemData: Partial<MenuItem> = {
        title: formData.title,
        categoryId: formData.categoryId,
        price: parseFloat(formData.price),
        prepTime: formData.prepTime ? parseInt(formData.prepTime) : undefined,
        description: formData.description,
        image: formData.image,
        caffeine: formData.caffeine || undefined,
        ingredients: formData.ingredients || undefined,
        allergens: formData.allergens || undefined,
        stockStatus,
        stockQuantity: formData.stockQuantity ? parseInt(formData.stockQuantity) : 0,
        isFeatured: formData.isFeatured,
        isAvailableForDelivery: formData.isAvailableForDelivery,
        isActive: formData.isActive,
      };
      
      // Update the menu item
      await updateMenuItem(id, menuItemData);
      
      // Redirect to menu items page
      router.push('/admin/menu-items');
    } catch (err) {
      console.error('Error updating menu item:', err);
      setError(err instanceof Error ? err.message : 'Failed to update menu item');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#56999B] dark:border-[#5DBDC0]"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6" dir={isRTL ? "rtl" : "ltr"}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            {isClient ? t('admin.editMenuItem.title') : 'Edit Menu Item'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isClient ? t('admin.editMenuItem.description') : 'Update your menu item details'}
          </p>
        </div>
        <Link
          href="/admin/menu-items"
          className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"
        >
          <i className={`fa-solid fa-times ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
          {isClient ? t('admin.editMenuItem.cancel') : 'Cancel'}
        </Link>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-lg">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.editMenuItem.itemName') : 'Item Name'} *
              </label>
              <input 
                type="text" 
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                required
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.editMenuItem.category') : 'Category'} *
              </label>
              <select 
                name="categoryId"
                value={formData.categoryId}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                required
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.editMenuItem.price') : 'Price ($)'} *
              </label>
              <input 
                type="number" 
                name="price"
                value={formData.price}
                onChange={handleChange}
                step="0.01" 
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
                required
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.editMenuItem.prepTime') : 'Preparation Time (mins)'}
              </label>
              <input 
                type="number" 
                name="prepTime"
                value={formData.prepTime}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.editMenuItem.stock') : 'Stock Quantity'}
              </label>
              <input 
                type="number" 
                name="stockQuantity"
                value={formData.stockQuantity}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]" 
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {isClient ? t('admin.editMenuItem.description') : 'Description'}
            </label>
            <textarea
              rows={4}
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
            ></textarea>
          </div>

          {/* Nutritional Information Section */}
          <div className="space-y-4">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
                <i className="fa-solid fa-leaf text-green-500 mr-2"></i>
                {isClient ? t('admin.editMenuItem.nutritionalInfo') : 'Nutritional Information'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {isClient ? t('admin.editMenuItem.nutritionalInfoDescription') : 'Provide nutritional details to help customers make informed choices'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <i className="fa-solid fa-bolt text-yellow-500 mr-1"></i>
                  {isClient ? t('admin.editMenuItem.caffeine') : 'Caffeine Content'}
                </label>
                <input
                  type="text"
                  name="caffeine"
                  value={formData.caffeine}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  placeholder={isClient ? t('admin.editMenuItem.caffeinePlaceholder') : '95mg, Low caffeine, Caffeine-free'}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('admin.editMenuItem.caffeineHint') : 'Enter caffeine amount (e.g., "95mg", "Low caffeine", "Caffeine-free") or leave empty'}
                </p>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <i className="fa-solid fa-list text-blue-500 mr-1"></i>
                  {isClient ? t('admin.editMenuItem.ingredients') : 'Ingredients'}
                </label>
                <input
                  type="text"
                  name="ingredients"
                  value={formData.ingredients}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  placeholder={isClient ? t('admin.editMenuItem.ingredientsPlaceholder') : 'Espresso, steamed milk, milk foam'}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('admin.editMenuItem.ingredientsHint') : 'List main ingredients separated by commas'}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <i className="fa-solid fa-triangle-exclamation text-red-500 mr-1"></i>
                {isClient ? t('admin.editMenuItem.allergens') : 'Allergens'}
              </label>
              <input
                type="text"
                name="allergens"
                value={formData.allergens}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                placeholder={isClient ? t('admin.editMenuItem.allergensPlaceholder') : 'Milk, nuts, gluten'}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {isClient ? t('admin.editMenuItem.allergensHint') : 'List potential allergens separated by commas'}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {isClient ? t('admin.editMenuItem.itemImage') : 'Item Image'}
            </label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              {formData.image && (
                <div className="mb-4">
                  <img 
                    src={formData.image} 
                    alt={formData.title} 
                    className="max-h-40 mx-auto object-contain rounded-lg"
                  />
                </div>
              )}
              <div className="space-y-2">
                <input
                  type="text"
                  name="image"
                  value={formData.image}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg"
                  placeholder={isClient ? t('admin.editMenuItem.imageURL') : 'Image URL'}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {isClient ? t('admin.editMenuItem.status') : 'Status'}
            </label>
            <div className="flex flex-wrap gap-4">
              <label className="flex items-center">
                <input 
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleChange}
                  className="form-checkbox text-[#56999B] dark:text-[#5DBDC0] rounded" 
                />
                <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                  {isClient ? t('admin.editMenuItem.active') : 'Active'}
                </span>
              </label>
              <label className="flex items-center">
                <input 
                  type="checkbox"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={handleChange}
                  className="form-checkbox text-[#56999B] dark:text-[#5DBDC0] rounded" 
                />
                <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                  {isClient ? t('admin.editMenuItem.featured') : 'Featured Item'}
                </span>
              </label>
              <label className="flex items-center">
                <input 
                  type="checkbox"
                  name="isAvailableForDelivery"
                  checked={formData.isAvailableForDelivery}
                  onChange={handleChange}
                  className="form-checkbox text-[#56999B] dark:text-[#5DBDC0] rounded" 
                />
                <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                  {isClient ? t('admin.editMenuItem.deliveryAvailable') : 'Available for Delivery'}
                </span>
              </label>
            </div>
          </div>

          <div className={`flex ${isRTL ? 'justify-start' : 'justify-end'} ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'} pt-6 border-t border-gray-200 dark:border-gray-700`}>
            <Link
              href="/admin/menu-items"
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832]"
            >
              {isClient ? t('admin.editMenuItem.cancel') : 'Cancel'}
            </Link>
            <button 
              type="submit" 
              className="px-6 py-2 bg-[#56999B] dark:bg-[#5DBDC0] text-white rounded-lg hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D] disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting 
                ? (isClient ? t('common.updating') : 'Updating...') 
                : (isClient ? t('admin.editMenuItem.saveChanges') : 'Save Changes')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 